//package com.yt.model;
//
//import lombok.SneakyThrows;
//import org.geotools.geometry.jts.JTSFactoryFinder;
//import org.locationtech.jts.geom.Coordinate;
//import org.locationtech.jts.geom.Polygon;
//import org.orekit.data.ClasspathCrawler;
//import org.orekit.data.DataContext;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//
//
//
//
//
//@SpringBootApplication
//public class TbModelApplication {
//
//    @SneakyThrows
//    public static void main(String[] args) {
//        var provider = new ClasspathCrawler("orekit-data-main.zip");
//        DataContext.getDefault().getDataProvidersManager().addProvider(provider);
//        SpringApplication.run(TbModelApplication.class, args);
//    }
//
//}


package com.yt.model;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.ToString;
import org.mybatis.spring.annotation.MapperScan;
import org.orekit.data.ClasspathCrawler;
import org.orekit.data.DataContext;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.io.File;
import java.util.*;


@lombok.Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
class Data {
    private long id;
    private String frequency_range;
    private int quantity;
    private double bandwidth;
    private String type;
    private long satellite_id;
    private double lon;
}

class ListData extends TypeReference<List<Data>> {
}

@SpringBootApplication
@MapperScan("com.yt.model.mapper")
public class TbModelApplication {

    @SneakyThrows
    public static void main(String[] args) {
        var provider = new ClasspathCrawler("orekit-data-main.zip");
        DataContext.getDefault().getDataProvidersManager().addProvider(provider);
        SpringApplication.run(TbModelApplication.class, args);

//        ObjectMapper objectMapper = new ObjectMapper();
//        List<Data> list = objectMapper.readValue(new File("D:/data.json"), new ListData());
//        Random random = new Random();
//
//
//        List<Map<String, Object>> maps = list.stream().map(data -> {
//            int quantity = data.getQuantity();
//            var mapList = new ArrayList<Map<String, Object>>();
//            for (int i = 0; i < quantity; i++) {
//                var map = new HashMap<String, Object>();
//                float value = random.nextFloat();
//                boolean is_fixed = value > 0.2;
//                double lat = random.nextDouble(-10, 10);
//                double lon = random.nextDouble(data.getLon() - 20, data.getLon() + 20);
//
//                map.put("freequency_range", data.getFrequency_range());
//                map.put("bandwidth", data.getBandwidth());
//                map.put("type", data.getType());
//
//                //  Is it fixed
//                map.put("is_fixed", is_fixed);
//                map.put("satellite_id", data.getSatellite_id());
//                map.put("lon", lon);
//                map.put("lat", lat);
//                mapList.add(map);
//            }
//            return mapList;
//        }).flatMap(List::stream).toList();
//
//        objectMapper.writeValue(new File("D:/base_geostationary_satellite_transponder.json"), maps);

    }

}
