package com.yt.model.mapper.data;

import com.yt.model.common.db.DB;
import com.yt.model.common.db.DataSourceContextHolder;
import com.yt.model.entity.data.BaseGeostationarySatelliteTransponder;
import com.yt.model.entity.data.BaseGeostationarySatelliteTransponderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

@DB(DataSourceContextHolder.SYS_USER)
public interface BaseGeostationarySatelliteTransponderMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite_transponder
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    long countByExample(BaseGeostationarySatelliteTransponderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite_transponder
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int deleteByExample(BaseGeostationarySatelliteTransponderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite_transponder
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int insert(BaseGeostationarySatelliteTransponder row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite_transponder
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int insertSelective(BaseGeostationarySatelliteTransponder row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite_transponder
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    List<BaseGeostationarySatelliteTransponder> selectByExample(BaseGeostationarySatelliteTransponderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite_transponder
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int updateByExampleSelective(@Param("row") BaseGeostationarySatelliteTransponder row, @Param("example") BaseGeostationarySatelliteTransponderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite_transponder
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int updateByExample(@Param("row") BaseGeostationarySatelliteTransponder row, @Param("example") BaseGeostationarySatelliteTransponderExample example);
}