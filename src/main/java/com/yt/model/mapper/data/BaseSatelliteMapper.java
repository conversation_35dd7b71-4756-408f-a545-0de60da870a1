package com.yt.model.mapper.data;

import com.yt.model.common.db.DB;
import com.yt.model.common.db.DataSourceContextHolder;
import com.yt.model.entity.data.BaseSatellite;
import com.yt.model.entity.data.BaseSatelliteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

@DB(DataSourceContextHolder.SYS_USER)
public interface BaseSatelliteMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    long countByExample(BaseSatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int deleteByExample(BaseSatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int insert(BaseSatellite row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int insertSelective(BaseSatellite row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    List<BaseSatellite> selectByExample(BaseSatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    BaseSatellite selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int updateByExampleSelective(@Param("row") BaseSatellite row, @Param("example") BaseSatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int updateByExample(@Param("row") BaseSatellite row, @Param("example") BaseSatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int updateByPrimaryKeySelective(BaseSatellite row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_satellite
     *
     * @mbg.generated Wed May 14 17:01:17 CST 2025
     */
    int updateByPrimaryKey(BaseSatellite row);
}