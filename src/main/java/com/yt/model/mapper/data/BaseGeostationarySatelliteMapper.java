package com.yt.model.mapper.data;

import com.yt.model.common.db.DB;
import com.yt.model.common.db.DataSourceContextHolder;
import com.yt.model.entity.data.BaseGeostationarySatellite;
import com.yt.model.entity.data.BaseGeostationarySatelliteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

@DB(DataSourceContextHolder.SYS_USER)
public interface BaseGeostationarySatelliteMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    long countByExample(BaseGeostationarySatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int deleteByExample(BaseGeostationarySatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int insert(BaseGeostationarySatellite row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int insertSelective(BaseGeostationarySatellite row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    List<BaseGeostationarySatellite> selectByExample(BaseGeostationarySatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    BaseGeostationarySatellite selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int updateByExampleSelective(@Param("row") BaseGeostationarySatellite row, @Param("example") BaseGeostationarySatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int updateByExample(@Param("row") BaseGeostationarySatellite row, @Param("example") BaseGeostationarySatelliteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int updateByPrimaryKeySelective(BaseGeostationarySatellite row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table base_geostationary_satellite
     *
     * @mbg.generated Tue May 13 15:37:34 CST 2025
     */
    int updateByPrimaryKey(BaseGeostationarySatellite row);
}