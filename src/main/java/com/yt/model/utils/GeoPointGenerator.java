package com.yt.model.utils;

import java.util.Random;

public class GeoPointGenerator {
    private static final double EARTH_RADIUS = 6371.0; // 地球半径，单位：公里
    private static final Random random = new Random();

    public static void main(String[] args) {
        double centerLat = 39.9042; // 中心点纬度，示例使用北京坐标
        double centerLng = 116.4074; // 中心点经度
        int pointCount = 10; // 生成的点数量
        
        // 生成并打印随机点
        for (int i = 0; i < pointCount; i++) {
            double[] point = generateRandomPoint(centerLat, centerLng, 8000);
            System.out.printf("点 %d: 纬度=%.6f, 经度=%.6f%n", i+1, point[0], point[1]);
        }
    }

    /**
     * 生成指定中心点周围指定半径内的随机地理坐标
     * @param centerLat 中心点纬度
     * @param centerLng 中心点经度
     * @param radius 半径（公里）
     * @return 包含纬度和经度的数组 [latitude, longitude]
     */
    public static double[] generateRandomPoint(double centerLat, double centerLng, double radius) {
        // 将角度转换为弧度
        double centerLatRad = Math.toRadians(centerLat);
        double centerLngRad = Math.toRadians(centerLng);
        
        // 生成随机距离和角度
        double distance = radius * Math.sqrt(random.nextDouble()); // 距离，使用平方根使点分布更均匀
        double bearing = 2 * Math.PI * random.nextDouble(); // 角度，0-2π
        
        // 计算新坐标
        double angularDistance = distance / EARTH_RADIUS;
        
        double newLatRad = Math.asin(
            Math.sin(centerLatRad) * Math.cos(angularDistance) +
            Math.cos(centerLatRad) * Math.sin(angularDistance) * Math.cos(bearing)
        );
        
        double newLngRad = centerLngRad + Math.atan2(
            Math.sin(bearing) * Math.sin(angularDistance) * Math.cos(centerLatRad),
            Math.cos(angularDistance) - Math.sin(centerLatRad) * Math.sin(newLatRad)
        );
        
        // 标准化经度到[-π, π]
        newLngRad = (newLngRad + 3 * Math.PI) % (2 * Math.PI) - Math.PI;
        
        // 将弧度转换为角度
        double newLat = Math.toDegrees(newLatRad);
        double newLng = Math.toDegrees(newLngRad);
        
        return new double[]{newLat, newLng};
    }
}