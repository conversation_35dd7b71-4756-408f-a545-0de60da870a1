package com.yt.model.service.model.impl;

import cn.hutool.core.lang.Tuple;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yt.model.dto.req.model.satellite.EllipseVm;
import com.yt.model.dto.req.model.satellite.EnvelopeInfo;
import com.yt.model.dto.req.model.satellite.Position;
import com.yt.model.dto.req.model.satellite.TimePosition;
import com.yt.model.dto.resp.model.SatelliteRespDTO;
import com.yt.model.entity.data.*;
import com.yt.model.mapper.data.*;
import com.yt.model.service.model.SatelliteModelService;
import com.yt.model.utils.GeoPointGenerator;
import com.yt.model.utils.GeoUtils;
import com.yt.model.utils.SatelliteUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Geometry;
import org.orekit.time.TimeScalesFactory;
import org.orekit.utils.Constants;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Math.acos;
import static java.lang.Math.cos;
import static java.lang.Math.pow;
import static java.lang.Math.sin;
import static java.lang.Math.sqrt;

@Service
public class SatelliteModelServiceImpl implements SatelliteModelService {

    private final BaseSatelliteMapper baseSatelliteMapper;
    private final BaseGeostationarySatelliteMapper baseGeostationarySatelliteMapper;
    private final BaseGeostationarySatelliteTransponderMapper baseGeostationarySatelliteTransponderMapper;
//    private final GeodeticCalculator geodeticCalculator = new GeodeticCalculator(DefaultGeographicCRS.WGS84);
    private final DataInstanceMapper dataInstanceMapper;
    private final DataInstancePzRefEquMapper dataInstancePzRefEquMapper;
    private final DataInstanceRefModelMapper dataInstanceRefModelMapper;


    public SatelliteModelServiceImpl(BaseSatelliteMapper baseSatelliteMapper, BaseGeostationarySatelliteMapper baseGeostationarySatelliteMapper, BaseGeostationarySatelliteTransponderMapper baseGeostationarySatelliteTransponderMapper, DataInstanceMapper dataInstanceMapper, DataInstancePzRefEquMapper dataInstancePzRefEquMapper, DataInstanceRefModelMapper dataInstanceRefModelMapper) {
        this.baseSatelliteMapper = baseSatelliteMapper;
        this.baseGeostationarySatelliteMapper = baseGeostationarySatelliteMapper;
        this.baseGeostationarySatelliteTransponderMapper = baseGeostationarySatelliteTransponderMapper;
        this.dataInstanceMapper = dataInstanceMapper;
        this.dataInstancePzRefEquMapper = dataInstancePzRefEquMapper;
        this.dataInstanceRefModelMapper = dataInstanceRefModelMapper;
    }

    @Override
    public List<SatelliteRespDTO> getPosition(Date date, String type) {
        var example = new BaseSatelliteExample();
        example.createCriteria().andTypeEqualTo(type);

        try (Page<Object> page = PageHelper.startPage(1, 7500)) {
            var list = baseSatelliteMapper.selectByExample(example);
            var utc = TimeScalesFactory.getUTC();

            return list.stream().map(baseSatellite ->
                    SatelliteUtils.getPosition(baseSatellite, date, utc)).collect(Collectors.toList());
        }
    }

    @Override
    public List<TimePosition> getRangePosition(Date startDate, Date endDate, int duration, String type) {
        var simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        var example = new BaseSatelliteExample();
        example.createCriteria().andTypeEqualTo(type);

        try (Page<Object> page = PageHelper.startPage(1, 7500)) {
            var list = baseSatelliteMapper.selectByExample(example);
            var utc = TimeScalesFactory.getUTC();

            return list.stream().map(baseSatellite -> {
                var timePosition = new TimePosition(baseSatellite.getId(), baseSatellite.getName(), new ArrayList<>());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                while (calendar.getTime().before(endDate)) {
                    Date date = calendar.getTime();
                    String format = simpleDateFormat.format(date);
                    SatelliteRespDTO position = SatelliteUtils.getPosition(baseSatellite, calendar.getTime(), utc);
                    List<?> route = List.of(format, position.getLon(), position.getLat(), position.getAlt());
                    calendar.add(Calendar.SECOND, duration);
                    timePosition.getRoute().add(route);
                }
                String format = simpleDateFormat.format(endDate);
                SatelliteRespDTO position = SatelliteUtils.getPosition(baseSatellite, calendar.getTime(), utc);
                List<?> route = List.of(format, position.getLon(), position.getLat(), position.getAlt());
                calendar.add(Calendar.SECOND, duration);
                timePosition.getRoute().add(route);
                return timePosition;
            }).toList();

        }
    }

    @Override
    public EllipseVm calculateSatelliteEllipse(Position satellitePosition, Position targetPosition) {
        // 地球半径[米]
        var R = Constants.WGS84_EARTH_EQUATORIAL_RADIUS;
        var theta3db = Math.toRadians(1.8);
        // 卫星位置
        var lngS = satellitePosition.getLon();
        var latS = satellitePosition.getLat();
        var h = satellitePosition.getAlt();

        // 波束瞄准线与地球表面交点
        var lng = targetPosition.getLon();
        var lat = targetPosition.getLat();
        // 小区中心点C与卫星在地面的投影点O的球面距离
        var OC = GeoUtils.calcDistance(lngS, latS, lng, lat);
        // 小区中心点C卫星直线距离DC
        var DC = sqrt((R + h) * (R + h) + R * R - 2 * R * (R + h) * cos(OC / R));
        // 小区中心相对于卫星正投影的方位角
        var thetaC = acos(
                (h * h + 2 * R * h + DC * DC)
                        / (2 * DC * (R + h))
        );
        // 小区近卫星点A与卫星正投影点O的距离
        var thetaA = thetaC - theta3db;
        // (R + h) * pow(sin(thetaA), 2)     3.1988678506012347E7
        //
        var OA = R * acos(((R + h) * pow(sin(thetaA), 2) + cos(thetaA) * sqrt((R + h) * (R + h) * cos(thetaA) * cos(thetaA) - (h * h + 2 * R * h))) * (1 / R));
        // 小区远卫星点B与卫星正投影点O的距离
        var thetaB = thetaC + theta3db;
        var OB = R * acos(((R + h) * pow(sin(thetaB), 2) + cos(thetaB) * sqrt(pow((R + h) * cos(thetaB), 2) - (h * h + 2 * R * h))) * (1 / R));
        // 椭圆小区半长轴 a
        var a = thetaA > 0 ? (OB - OA) / 2 : (OB + OA) / 2;
        // 卫星S到小区几何中心点D的距离为
        var SD = sqrt((h + R) * (h + R) + R * R - 2 * R * (R + h) * cos((OB - a) / R));
        // 椭圆小区半短轴 b
        var b = sqrt(pow(SD * SD + DC * DC - pow(OB - OC - a, 2), 2) / pow(2 * DC * cos(theta3db), 2) - (SD * SD));
        var angle = GeoUtils.northAngle(lngS, latS, lng, lat);
        return new EllipseVm(a, b, targetPosition, angle);
    }

    @Override
    public Tuple calculateSatelliteBeam(Position satellitePosition, List<Position> targetPositions) {
        if (!targetPositions.isEmpty()) {
            var polygons = targetPositions.stream().map(target -> calculateSatelliteEllipse(satellitePosition, target))
                    .map(ellipsePosition -> GeoUtils.createEllipse(
                            ellipsePosition.getPosition().getLon(),
                            ellipsePosition.getPosition().getLat(),
                            ellipsePosition.getSemiMajorAxis(),
                            ellipsePosition.getSemiMajorAxis(),
                            ellipsePosition.getAzimuth()
                    )).toList();
            // 合并多个椭圆
            Geometry geometry = polygons.getFirst();
            for (var i = 1; i < polygons.size(); i++) {
                geometry = geometry.union(polygons.get(i));
            }
            // 获取椭圆面积
            double area = geometry.getArea();
            // 获取椭圆的坐标并转换为List<Double>
            var resultList = new ArrayList<List<List<Double>>>();
            for (int i = 0; i < geometry.getNumGeometries(); i++) {
                var n = geometry.getGeometryN(i);
                var flatten = Arrays.stream(n.getCoordinates()).map(coordinate -> List.of(coordinate.getX(), coordinate.getY())).toList();
                resultList.add(flatten);
            }
            // 返回椭圆和面积
            return new Tuple(resultList, area);
        }
        return null;
    }

    @Override
    public List<BaseGeostationarySatellite> listGeostationarySatellites() {
        var example = new BaseGeostationarySatelliteExample();
        return baseGeostationarySatelliteMapper.selectByExample(example);
    }

    @Override
    public EnvelopeInfo listGsFixedEllipse(String id, Boolean isFixed) {
        DataInstancePzRefEquExample where = new DataInstancePzRefEquExample();
        where.createCriteria().andInstancePtIdEqualTo(id);
        //装备的数量
        var satelliteEquList =  dataInstancePzRefEquMapper.selectByExample(where);
        if (satelliteEquList == null) {
            throw new RuntimeException("satellite not found");
        }
        if (satelliteEquList.isEmpty()) {
            throw new RuntimeException("satellite has  not equipment");
        }

        DataInstanceRefModelExample wherePtPro = new DataInstanceRefModelExample();
        wherePtPro.createCriteria().andDataInstanceIdEqualTo(id);

        // wx的位置
        var dataInstanceRefModels = dataInstanceRefModelMapper.selectByExample(wherePtPro);
        String longitude = "";
        String height = "";
        Optional<DataInstanceRefModel> longitudeFirst = dataInstanceRefModels.stream().filter(x -> "轨道位置".equals(x.getChName())).findFirst();
        if (longitudeFirst.isPresent()) {
            longitude = longitudeFirst.get().getDefaultValue();
        }

        if (StringUtils.isBlank(longitude)) {
            throw new RuntimeException("satellite轨道位置属性配置错误");
        }

        Optional<DataInstanceRefModel> gdFirst = dataInstanceRefModels.stream().filter(x -> "轨道高度".equals(x.getChName())).findFirst();
        if (gdFirst.isPresent()) {
            height = gdFirst.get().getDefaultValue();
        }

        if (StringUtils.isBlank(height)) {
            throw new RuntimeException("satellite轨道高度属性配置错误");
        }
        // 获取带宽信息
        wherePtPro.clear();
        wherePtPro.createCriteria().andDataInstanceIdIn(satelliteEquList.stream()
                .map(DataInstancePzRefEqu::getInstancePtId).toList()).andChNameEqualTo("带宽");
        var baseGeostationarySatelliteTransponders = dataInstanceRefModelMapper.selectByExample(wherePtPro);

        // 根据wx的数量，计算出每个地球上每个点的位置
        var positions = new ArrayList<Position>();
        for (int i = 0; i < satelliteEquList.size(); i++) {
            var point = GeoPointGenerator.generateRandomPoint(Double.parseDouble(longitude), 0, 1000);
            positions.add(new Position(point[1], point[0], 0.0));
        }

        // wx的位置
        var satellitePosition = new Position(Double.parseDouble(longitude), null, Double.parseDouble(height));
        // 包络信息
        var tuple = this.calculateSatelliteBeam(satellitePosition, positions);
        // 包络数据
        List<List<List<Double>>> polygons = tuple.get(0);
        // 面积
        double area = tuple.get(1);
        // 带宽信息
        var bandWidth = baseGeostationarySatelliteTransponders.stream()
                .mapToDouble(transponder -> Optional.of(Double.parseDouble(transponder
                        .getDefaultValue())).orElse(0.0))
                .sum();
        // 入网率，当前设置为 每个用户 1Mbps带宽 ，用户数 = (B_max / B_user) * 0.5
        // [由于资源分配效率、突发流量、协议开销等因素，实际容量通常为理论值的 30%~70%。这里取0.5]，算出人数向上取整
        int userNum = (int) Math.ceil((bandWidth) * 0.5);
        return new EnvelopeInfo(polygons, bandWidth + " Mbps", userNum);
    }

}
