spring:
  application:
    name: tb-model
  datasource:
    user:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ********************************************************************************************************************************************************************************
      username: root
      password: 4uVaKAblkr0irNa
    basic:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ****************************************************************************************************************************************************************************
      username: root
      password: 4uVaKAblkr0irNa
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
server:
  port: 5001

mybatis:
  mapper-locations: classpath:/mapper/**/**/**.xml
  type-aliases-package: com.yt.model.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
